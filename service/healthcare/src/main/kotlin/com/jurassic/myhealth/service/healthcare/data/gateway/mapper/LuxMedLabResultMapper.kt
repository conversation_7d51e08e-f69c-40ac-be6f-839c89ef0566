package com.jurassic.myhealth.service.healthcare.data.gateway.mapper

import com.fasterxml.jackson.databind.ObjectMapper
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.laboratory.ExaminationRemote
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.laboratory.LabResultResponse
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.laboratory.LabResultStatusRemote
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.laboratory.TestResultRemote
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.timeline.EventDetailsResponse
import com.jurassic.myhealth.service.healthcare.domain.model.*
import mu.KotlinLogging
import org.springframework.stereotype.Component
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

private val logger = KotlinLogging.logger {}

/**
 * Mapper for converting LuxMed laboratory results to domain models
 */
@Component
class LuxMedLabResultMapper(
    private val objectMapper: ObjectMapper
) {
    
    private val dateTimeFormatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME
    
    fun mapLabResultFromEventDetails(
        userId: UserId, 
        eventDetailsResponse: EventDetailsResponse
    ): LabResult? {
        return try {
            val labResultResponse = convertToLabResultResponse(eventDetailsResponse)
            mapLabResult(userId, labResultResponse)
        } catch (e: Exception) {
            logger.error(e) { "Failed to map lab result from event details for event ${eventDetailsResponse.id}" }
            null
        }
    }
    
    /**
     * Convert LabResultResponse to domain LabResult
     */
    fun mapLabResult(userId: UserId, labResultResponse: LabResultResponse): LabResult? {
        if (labResultResponse.eventId == 0L) return null
        
        val observationDate = parseDateTime(labResultResponse.date) ?: return null
        
        val parameters = labResultResponse.examinations.flatMap { examination ->
            mapExaminationToParameters(examination)
        }
        
        val testName = labResultResponse.examinations.firstOrNull()?.name?.takeIf { it.isNotBlank() }
            ?: labResultResponse.title.ifBlank { "Laboratory Test" }
        
        return LabResult(
            userId = userId,
            externalId = labResultResponse.eventId.toString(),
            sourceSystem = SourceSystem.LUXMED,
            testName = testName,
            parameters = parameters,
            observationDate = observationDate,
            status = mapEventStatus(labResultResponse.status),
            reportUrl = null, // Could be populated from results.downloadUrl if available
            notes = labResultResponse.examinations.firstOrNull()?.comment?.comment
        )
    }
    
    /**
     * Map ExaminationRemote to list of LabParameters
     */
    private fun mapExaminationToParameters(examination: ExaminationRemote): List<LabParameter> {
        return examination.results.mapNotNull { testResult ->
            mapTestResultToParameter(testResult)
        }
    }
    
    /**
     * Map TestResultRemote to LabParameter
     */
    private fun mapTestResultToParameter(testResult: TestResultRemote): LabParameter? {
        if (testResult.status == LabResultStatusRemote.INFORMATION.id) {
            return null
        }

        val interpretation = mapResultStatus(testResult.status)

        return LabParameter(
            name = testResult.name,
            value = testResult.value,
            unit = testResult.measurement.takeIf { it.isNotBlank() },
            min = testResult.minScopeFormatted?.takeIf { it.isNotBlank() },
            max = testResult.maxScopeFormatted?.takeIf { it.isNotBlank() },
            interpretation = interpretation,
            notes = null
        )
    }
    
    /**
     * Build reference range string from min and max values
     */
    private fun buildReferenceRange(minFormatted: String?, maxFormatted: String?): String? {
        return when {
            !minFormatted.isNullOrBlank() && !maxFormatted.isNullOrBlank() -> "$minFormatted-$maxFormatted"
            !minFormatted.isNullOrBlank() -> ">$minFormatted"
            !maxFormatted.isNullOrBlank() -> "<$maxFormatted"
            else -> null
        }
    }
    
    /**
     * Map result status code to interpretation string
     */
    private fun mapResultStatus(statusCode: Int): String? {
        return when (LabResultStatusRemote.fromId(statusCode)) {
            LabResultStatusRemote.NORMAL -> "Normal"
            LabResultStatusRemote.ABNORMAL -> "Abnormal"
            LabResultStatusRemote.CRITICAL -> "Critical"
            LabResultStatusRemote.INFORMATION -> "Information"
            LabResultStatusRemote.UNKNOWN -> null
        }
    }
    
    /**
     * Convert EventDetailsResponse to LabResultResponse
     * This is needed because the API returns different structures for different event types
     */
    private fun convertToLabResultResponse(eventDetailsResponse: EventDetailsResponse): LabResultResponse {
        val jsonString = objectMapper.writeValueAsString(eventDetailsResponse)
        return objectMapper.readValue(jsonString, LabResultResponse::class.java)
    }
    
    private fun parseDateTime(dateString: String): LocalDateTime? {
        return try {
            LocalDateTime.parse(dateString, dateTimeFormatter)
        } catch (e: Exception) {
            logger.warn { "Failed to parse date: $dateString" }
            null
        }
    }
    
    private fun mapEventStatus(statusCode: Int): String {
        return when (statusCode) {
            1 -> "Completed"
            2 -> "Scheduled"
            3 -> "Cancelled"
            4 -> "In Progress"
            5 -> "No Show"
            else -> "Unknown"
        }
    }
}
