package com.jurassic.myhealth.service.healthcare.api.model

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * DTO for lab parameters
 */
data class LabParameterDto(
    /**
     * Name of the parameter
     */
    @JsonProperty("name")
    val name: String,
    
    /**
     * Measured value
     */
    @JsonProperty("value")
    val value: String,
    
    /**
     * Unit of measurement
     */
    @JsonProperty("unit")
    val unit: String?,
    
    /**
     * Minimum reference value for normal range
     */
    @JsonProperty("min")
    val min: String?,

    /**
     * Maximum reference value for normal range
     */
    @JsonProperty("max")
    val max: String?,
    
    /**
     * Interpretation of the result
     */
    @JsonProperty("interpretation")
    val interpretation: String?,
    
    /**
     * Additional notes
     */
    @JsonProperty("notes")
    val notes: String?
)
