package com.jurassic.myhealth.service.healthcare.domain.model

/**
 * Represents a specific parameter within a lab result
 */
data class LabParameter(
    /**
     * Name of the parameter (e.g., "Hemoglobin", "Glucose")
     */
    val name: String,
    
    /**
     * Measured value
     */
    val value: String,
    
    /**
     * Unit of measurement (e.g., "g/dL", "mg/dL")
     */
    val unit: String?,
    
    /**
     * Minimum reference value for normal range
     */
    val min: String?,

    /**
     * Maximum reference value for normal range
     */
    val max: String?,
    
    /**
     * Interpretation of the result (e.g., "Normal", "High", "Low")
     */
    val interpretation: String?,
    
    /**
     * Additional notes or comments
     */
    val notes: String? = null
) {
    init {
        require(name.isNotBlank()) { "Parameter name cannot be blank" }
        require(value.isNotBlank()) { "Parameter value cannot be blank" }
    }
}
