package com.jurassic.myhealth.service.healthcare.data.repository.mapper

import com.jurassic.myhealth.service.healthcare.data.repository.document.LabParameterDocument
import com.jurassic.myhealth.service.healthcare.data.repository.document.LabResultDocument
import com.jurassic.myhealth.service.healthcare.domain.model.LabParameter
import com.jurassic.myhealth.service.healthcare.domain.model.LabResult
import com.jurassic.myhealth.service.healthcare.domain.model.SourceSystem
import com.jurassic.myhealth.service.healthcare.domain.model.UserId
import org.springframework.stereotype.Component

/**
 * Mapper between LabResult domain model and LabResultDocument
 */
@Component
class LabResultMapper {
    
    fun toDocument(labResult: LabResult): LabResultDocument {
        return LabResultDocument(
            id = labResult.id,
            userId = labResult.userId.value,
            externalId = labResult.externalId,
            sourceSystem = labResult.sourceSystem.name,
            testName = labResult.testName,
            parameters = labResult.parameters.map { toParameterDocument(it) },
            observationDate = labResult.observationDate,
            status = labResult.status,
            reportUrl = labResult.reportUrl,
            notes = labResult.notes,
            createdAt = labResult.createdAt,
            updatedAt = labResult.updatedAt
        )
    }
    
    fun toDomain(document: LabResultDocument): LabResult {
        return LabResult(
            id = document.id,
            userId = UserId(document.userId),
            externalId = document.externalId,
            sourceSystem = SourceSystem.valueOf(document.sourceSystem),
            testName = document.testName,
            parameters = document.parameters.map { toParameterDomain(it) },
            observationDate = document.observationDate,
            status = document.status,
            reportUrl = document.reportUrl,
            notes = document.notes,
            createdAt = document.createdAt,
            updatedAt = document.updatedAt
        )
    }
    
    private fun toParameterDocument(parameter: LabParameter): LabParameterDocument {
        return LabParameterDocument(
            name = parameter.name,
            value = parameter.value,
            unit = parameter.unit,
            min = parameter.min,
            max = parameter.max,
            interpretation = parameter.interpretation,
            notes = parameter.notes
        )
    }
    
    private fun toParameterDomain(document: LabParameterDocument): LabParameter {
        return LabParameter(
            name = document.name,
            value = document.value,
            unit = document.unit,
            referenceRange = document.referenceRange,
            interpretation = document.interpretation,
            notes = document.notes
        )
    }
}
