package com.jurassic.myhealth.service.healthcare.api.mapper

import com.jurassic.myhealth.service.healthcare.api.model.LabParameterDto
import com.jurassic.myhealth.service.healthcare.api.model.LabResultDto
import com.jurassic.myhealth.service.healthcare.domain.model.LabParameter
import com.jurassic.myhealth.service.healthcare.domain.model.LabResult
import org.springframework.stereotype.Component

/**
 * Mapper between LabResult domain model and LabResultDto
 */
@Component
class LabResultDtoMapper {
    
    fun toDto(labResult: LabResult): LabResultDto {
        return LabResultDto(
            id = labResult.id,
            externalId = labResult.externalId,
            sourceSystem = labResult.sourceSystem.name,
            testName = labResult.testName,
            parameters = labResult.parameters.map { toParameterDto(it) },
            observationDate = labResult.observationDate,
            status = labResult.status,
            reportUrl = labResult.reportUrl,
            notes = labResult.notes,
            createdAt = labResult.createdAt,
            updatedAt = labResult.updatedAt
        )
    }
    
    fun toDtoList(labResults: List<LabResult>): List<LabResultDto> {
        return labResults.map { toDto(it) }
    }
    
    private fun toParameterDto(parameter: LabParameter): LabParameterDto {
        return LabParameterDto(
            name = parameter.name,
            value = parameter.value,
            unit = parameter.unit,
            min = parameter.min,
            max = parameter.max,
            interpretation = parameter.interpretation,
            notes = parameter.notes
        )
    }
}
