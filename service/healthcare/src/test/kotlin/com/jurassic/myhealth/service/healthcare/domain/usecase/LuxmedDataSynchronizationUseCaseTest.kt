package com.jurassic.myhealth.service.healthcare.domain.usecase

import com.jurassic.myhealth.service.healthcare.domain.gateway.LuxmedMedGateway
import com.jurassic.myhealth.service.healthcare.domain.gateway.LuxmedMedicalData
import com.jurassic.myhealth.service.healthcare.domain.model.*
import com.jurassic.myhealth.service.healthcare.domain.repository.LabResultRepository
import com.jurassic.myhealth.service.healthcare.domain.repository.VisitRepository
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class LuxmedDataSynchronizationUseCaseTest {

    private val luxmedMedGateway = mockk<LuxmedMedGateway>()
    private val labResultRepository = mockk<LabResultRepository>()
    private val visitRepository = mockk<VisitRepository>()

    private val useCase = LuxmedDataSynchronizationUseCase(
        luxmedMedGateway,
        labResultRepository,
        visitRepository
    )

    @Test
    fun `should successfully synchronize medical data`() = runTest {
        // Given
        val userId = UserId("test-user-id")
        val luxmedToken = "test-token"

        val labResult = LabResult(
            userId = userId,
            externalId = "lab-1",
            sourceSystem = SourceSystem.LUXMED,
            testName = "Blood Test",
            parameters = listOf(
                LabParameter(
                    name = "Hemoglobin",
                    value = "14.5",
                    unit = "g/dL",
                    min = "12.0",
                    max = "15.5",
                    interpretation = "Normal"
                )
            ),
            observationDate = LocalDateTime.now(),
            status = "Final"
        )

        val visit = Visit(
            userId = userId,
            externalId = "visit-1",
            sourceSystem = SourceSystem.LUXMED,
            visitDate = LocalDateTime.now(),
            doctorName = "Dr. Smith",
            specialization = "Cardiology",
            facility = "Test Clinic",
            reason = "Checkup",
            recommendations = "Continue medication",
            status = VisitStatus.COMPLETED,
            visitType = "In-person"
        )

        val medicalData = LuxmedMedicalData(
            labResults = listOf(labResult),
            visits = listOf(visit)
        )

        coEvery { luxmedMedGateway.fetchAllResults(userId, luxmedToken) } returns Result.success(medicalData)
        coEvery { labResultRepository.save(labResult) } returns labResult
        coEvery { visitRepository.save(visit) } returns visit

        // When
        val result = useCase.execute(userId, luxmedToken)

        // Then
        assertTrue(result.isSuccess)

        coVerify { luxmedMedGateway.fetchAllResults(userId, luxmedToken) }
        coVerify { labResultRepository.save(labResult) }
        coVerify { visitRepository.save(visit) }
    }

    @Test
    fun `should handle gateway failure`() = runTest {
        // Given
        val userId = UserId("test-user-id")
        val luxmedToken = "test-token"
        val exception = RuntimeException("Gateway error")

        coEvery { luxmedMedGateway.fetchAllResults(userId, luxmedToken) } returns Result.failure(exception)

        // When
        val result = useCase.execute(userId, luxmedToken)

        // Then
        assertTrue(result.isFailure)
        assertEquals(exception, result.exceptionOrNull())
    }
}
